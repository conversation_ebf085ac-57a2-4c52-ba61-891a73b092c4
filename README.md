# Face Recognition Attendance System

A complete face recognition attendance system with a modern web interface. The system uses AI-powered face recognition to automatically track attendance in real-time.

## 🌟 Features

- **User Enrollment**: Capture and register faces with names
- **Real-time Recognition**: Live webcam feed with face detection and recognition
- **Attendance Logging**: Automatic attendance tracking with timestamps
- **Modern UI**: Responsive, professional web interface
- **CSV Storage**: Simple file-based storage (no database required)
- **Visual Feedback**: Green border for recognized faces, red for unknown
- **Attendance Reports**: View and filter attendance logs by date
- **Offline Operation**: Works completely offline without internet dependency

## 📁 Project Structure

```
FaceAttendence/
├── backend/
│   ├── app.py              # Flask API server
│   ├── models.py           # CSV storage management
│   ├── requirements.txt    # Python dependencies
│   ├── users.csv          # User data (auto-generated)
│   ├── attendance.csv     # Attendance logs (auto-generated)
│   └── encodings.pkl      # Face encodings (auto-generated)
├── frontend/
│   ├── index.html         # Main web interface
│   ├── style.css          # Modern CSS styling
│   └── script.js          # JavaScript functionality
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.7 or higher
- Webcam/Camera access
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Installation

1. **Clone or download the project**
   ```bash
   cd FaceAttendence
   ```

2. **Install Python dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

   **Note**: If you encounter issues installing `face-recognition`, you may need to install additional dependencies:
   
   **On Windows:**
   ```bash
   pip install cmake
   pip install dlib
   pip install face-recognition
   ```
   
   **On macOS:**
   ```bash
   brew install cmake
   pip install dlib
   pip install face-recognition
   ```
   
   **On Linux (Ubuntu/Debian):**
   ```bash
   sudo apt-get update
   sudo apt-get install build-essential cmake
   sudo apt-get install libopenblas-dev liblapack-dev
   sudo apt-get install libx11-dev libgtk-3-dev
   pip install dlib
   pip install face-recognition
   ```

3. **Start the backend server**
   ```bash
   python app.py
   ```
   
   You should see:
   ```
   Starting Face Recognition Attendance System Backend...
   Available endpoints:
     GET  /health - Health check
     POST /enroll - Enroll new user
     POST /recognize - Recognize faces
     GET  /attendance - Get attendance logs
     GET  /users - Get enrolled users
   * Running on http://0.0.0.0:5000
   ```

4. **Open the frontend**
   - Open `frontend/index.html` in your web browser
   - Or serve it using a local server:
   ```bash
   cd frontend
   python -m http.server 8000
   # Then open http://localhost:8000 in your browser
   ```

## 📖 How to Use

### 1. Enroll Users

1. Click on the **"Enrollment"** tab
2. Enter the person's full name
3. Click **"Start Camera"** to activate the webcam
4. Position the face within the guide circle
5. Click **"Capture Photo"** when ready
6. Review the captured image
7. Click **"Enroll User"** to save the face encoding

### 2. Start Attendance

1. Click on the **"Attendance"** tab
2. Click **"Start Attendance"** to begin real-time recognition
3. The system will automatically:
   - Detect faces in the video feed
   - Recognize enrolled users
   - Show green border for recognized faces
   - Show red border for unknown faces
   - Log attendance automatically

### 3. View Attendance Logs

1. Click on the **"Attendance Logs"** tab
2. View all attendance records in the table
3. Filter by specific date using the date picker
4. Click **"Refresh"** to update the logs

## 🔧 API Endpoints

The backend provides the following REST API endpoints:

- **GET /health** - Check if the server is running
- **POST /enroll** - Enroll a new user with face image
- **POST /recognize** - Recognize faces in an image
- **GET /attendance** - Get attendance logs (with optional date filter)
- **GET /users** - Get list of enrolled users

## 📊 Data Storage

The system uses CSV files for data storage:

- **users.csv**: Stores user information (ID, name, creation date)
- **attendance.csv**: Stores attendance logs (ID, user ID, name, timestamp, confidence)
- **encodings.pkl**: Stores face encodings as pickled numpy arrays

## 🎨 UI Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern Interface**: Clean, professional design with smooth animations
- **Real-time Updates**: Live attendance tracking and log updates
- **Visual Feedback**: Color-coded recognition status
- **Connection Status**: Shows backend connectivity status
- **Loading Indicators**: User-friendly loading states
- **Notifications**: Success/error messages for user actions

## 🔒 Security & Privacy

- **Local Processing**: All face recognition happens locally
- **No Cloud Dependency**: Works completely offline
- **File-based Storage**: Simple CSV files, no database required
- **Privacy Focused**: Face data never leaves your local machine

## 🛠️ Troubleshooting

### Common Issues

1. **Camera not working**
   - Check browser permissions for camera access
   - Ensure no other application is using the camera
   - Try refreshing the page

2. **Backend connection failed**
   - Ensure the Flask server is running on port 5000
   - Check if any firewall is blocking the connection
   - Verify the API_BASE_URL in script.js matches your server

3. **Face recognition not working**
   - Ensure good lighting conditions
   - Position face clearly within the camera view
   - Make sure the person is enrolled in the system

4. **Installation issues**
   - For face-recognition library issues, see the installation section above
   - Ensure you have Python 3.7+ installed
   - Try creating a virtual environment

### Performance Tips

- **Good Lighting**: Ensure adequate lighting for better recognition
- **Clear Images**: Use high-quality images for enrollment
- **Single Face**: Enroll with images containing only one face
- **Consistent Angles**: Try to maintain similar face angles during enrollment and recognition

## 🤝 Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **face_recognition library** by Adam Geitgey
- **OpenCV** for computer vision capabilities
- **Flask** for the web framework
- **Font Awesome** for icons

---

**Enjoy using the Face Recognition Attendance System! 🎉**
