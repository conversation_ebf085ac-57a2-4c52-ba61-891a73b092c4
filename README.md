# Face Recognition Attendance System

A complete face recognition attendance system with a modern web interface. The system uses AI-powered face recognition to automatically track attendance in real-time.

## 🌟 Features

- **User Enrollment**: Capture and register faces with names
- **Real-time Recognition**: Live webcam feed with face detection and recognition
- **Attendance Logging**: Automatic attendance tracking with timestamps
- **Modern UI**: Responsive, professional web interface
- **CSV Storage**: Simple file-based storage (no database required)
- **Visual Feedback**: Green border for recognized faces, red for unknown
- **Attendance Reports**: View and filter attendance logs by date
- **Offline Operation**: Works completely offline without internet dependency

## 📁 Project Structure

```
FaceAttendence/
├── backend/
│   ├── app.py              # Flask API server
│   ├── models.py           # CSV storage management
│   ├── requirements.txt    # Python dependencies
│   ├── users.csv          # User data (auto-generated)
│   ├── attendance.csv     # Attendance logs (auto-generated)
│   └── encodings.pkl      # Face encodings (auto-generated)
├── frontend/
│   ├── index.html         # Main web interface
│   ├── style.css          # Modern CSS styling
│   └── script.js          # JavaScript functionality
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.7 or higher
- Webcam/Camera access
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Installation

#### Option 1: Automatic Installation (Recommended)

1. **Run the installation script**
   ```bash
   python install_dependencies.py
   ```

   This script will:
   - Check your Python version
   - Install system dependencies
   - Handle Python 3.12 compatibility issues
   - Create a simplified version if full installation fails

#### Option 2: Manual Installation

1. **Install system dependencies first**

   **On Linux (Ubuntu/Debian):**
   ```bash
   sudo apt-get update
   sudo apt-get install build-essential cmake
   sudo apt-get install libopenblas-dev liblapack-dev
   sudo apt-get install libx11-dev libgtk-3-dev python3-dev
   ```

   **On macOS:**
   ```bash
   brew install cmake
   ```

   **On Windows:**
   - Install Visual Studio Build Tools
   - Install CMake from https://cmake.org/download/

2. **Install Python dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

   **If you get errors with Python 3.12+:**
   ```bash
   # Install packages one by one
   pip install setuptools wheel
   pip install Flask==2.3.3 flask-cors==4.0.0
   pip install opencv-python==******** Pillow==10.0.1
   pip install numpy==1.26.4
   pip install dlib==19.24.2
   pip install face-recognition==1.3.0
   ```

#### Option 3: Simplified Mode (If face-recognition fails)

If you can't install the face-recognition library, use the simplified version:

1. **Install basic dependencies**
   ```bash
   pip install Flask flask-cors opencv-python Pillow numpy
   ```

2. **Use the simplified backend**
   ```bash
   python backend/app_simple.py
   ```

### Starting the System

1. **Start the backend server**
   ```bash
   # Full version
   python backend/app.py

   # OR simplified version
   python backend/app_simple.py
   ```

2. **Open the frontend**
   - Open `frontend/index.html` in your web browser
   - Or serve it using a local server:
   ```bash
   cd frontend
   python -m http.server 8000
   # Then open http://localhost:8000 in your browser
   ```

## 📖 How to Use

### 1. Enroll Users

1. Click on the **"Enrollment"** tab
2. Enter the person's full name
3. Click **"Start Camera"** to activate the webcam
4. Position the face within the guide circle
5. Click **"Capture Photo"** when ready
6. Review the captured image
7. Click **"Enroll User"** to save the face encoding

### 2. Start Attendance

1. Click on the **"Attendance"** tab
2. Click **"Start Attendance"** to begin real-time recognition
3. The system will automatically:
   - Detect faces in the video feed
   - Recognize enrolled users
   - Show green border for recognized faces
   - Show red border for unknown faces
   - Log attendance automatically

### 3. View Attendance Logs

1. Click on the **"Attendance Logs"** tab
2. View all attendance records in the table
3. Filter by specific date using the date picker
4. Click **"Refresh"** to update the logs

## 🔧 API Endpoints

The backend provides the following REST API endpoints:

- **GET /health** - Check if the server is running
- **POST /enroll** - Enroll a new user with face image
- **POST /recognize** - Recognize faces in an image
- **GET /attendance** - Get attendance logs (with optional date filter)
- **GET /users** - Get list of enrolled users

## 📊 Data Storage

The system uses CSV files for data storage:

- **users.csv**: Stores user information (ID, name, creation date)
- **attendance.csv**: Stores attendance logs (ID, user ID, name, timestamp, confidence)
- **encodings.pkl**: Stores face encodings as pickled numpy arrays

## 🎨 UI Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern Interface**: Clean, professional design with smooth animations
- **Real-time Updates**: Live attendance tracking and log updates
- **Visual Feedback**: Color-coded recognition status
- **Connection Status**: Shows backend connectivity status
- **Loading Indicators**: User-friendly loading states
- **Notifications**: Success/error messages for user actions

## 🔒 Security & Privacy

- **Local Processing**: All face recognition happens locally
- **No Cloud Dependency**: Works completely offline
- **File-based Storage**: Simple CSV files, no database required
- **Privacy Focused**: Face data never leaves your local machine

## 🛠️ Troubleshooting

### Common Issues

1. **Camera not working**
   - Check browser permissions for camera access
   - Ensure no other application is using the camera
   - Try refreshing the page

2. **Backend connection failed**
   - Ensure the Flask server is running on port 5000
   - Check if any firewall is blocking the connection
   - Verify the API_BASE_URL in script.js matches your server

3. **Face recognition not working**
   - Ensure good lighting conditions
   - Position face clearly within the camera view
   - Make sure the person is enrolled in the system

4. **Installation issues**

   **Python 3.12+ Issues:**
   ```bash
   # If you get "No module named 'distutils'" error
   sudo apt-get install python3-distutils  # Linux
   # OR use the simplified installation script
   python install_dependencies.py
   ```

   **Face-recognition library issues:**
   ```bash
   # Try installing dependencies first
   pip install cmake dlib
   pip install face-recognition

   # If still failing, use conda
   conda install -c conda-forge dlib
   pip install face-recognition

   # Last resort: use simplified mode
   python backend/app_simple.py
   ```

   **General tips:**
   - Ensure you have Python 3.7+ installed
   - Try creating a virtual environment:
     ```bash
     python -m venv venv
     source venv/bin/activate  # Linux/Mac
     # OR
     venv\Scripts\activate     # Windows
     ```

### Performance Tips

- **Good Lighting**: Ensure adequate lighting for better recognition
- **Clear Images**: Use high-quality images for enrollment
- **Single Face**: Enroll with images containing only one face
- **Consistent Angles**: Try to maintain similar face angles during enrollment and recognition

## 🤝 Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **face_recognition library** by Adam Geitgey
- **OpenCV** for computer vision capabilities
- **Flask** for the web framework
- **Font Awesome** for icons

---

**Enjoy using the Face Recognition Attendance System! 🎉**
