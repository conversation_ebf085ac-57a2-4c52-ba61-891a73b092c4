#!/usr/bin/env python3
"""
Test script to verify the CSV-based storage system works correctly
"""
import sys
import os
import numpy as np

# Add backend directory to path
sys.path.append('backend')

from models import CSVManager

def test_csv_system():
    """Test the CSV storage system"""
    print("Testing CSV-based Face Recognition Storage System...")
    print("=" * 50)
    
    # Initialize CSV manager
    csv_manager = CSVManager(
        users_file='test_users.csv',
        attendance_file='test_attendance.csv',
        encodings_file='test_encodings.pkl'
    )
    
    print("✓ CSV Manager initialized successfully")
    
    # Test adding users
    print("\n1. Testing user enrollment...")
    
    # Create dummy face encodings (normally these would come from face_recognition library)
    dummy_encoding1 = np.random.rand(128)  # Face encodings are typically 128-dimensional
    dummy_encoding2 = np.random.rand(128)
    
    # Add test users
    user1_id = csv_manager.add_user("<PERSON>", dummy_encoding1)
    user2_id = csv_manager.add_user("<PERSON>", dummy_encoding2)
    
    if user1_id and user2_id:
        print(f"✓ Added user '<PERSON>' with ID: {user1_id}")
        print(f"✓ Added user '<PERSON>' with ID: {user2_id}")
    else:
        print("✗ Failed to add users")
        return False
    
    # Test duplicate user
    duplicate_id = csv_manager.add_user("John Doe", dummy_encoding1)
    if duplicate_id is None:
        print("✓ Correctly rejected duplicate user")
    else:
        print("✗ Should have rejected duplicate user")
    
    # Test getting all users
    print("\n2. Testing user retrieval...")
    users = csv_manager.get_all_users()
    print(f"✓ Retrieved {len(users)} users")
    
    for user_id, name, encoding in users:
        print(f"  - User ID: {user_id}, Name: {name}, Encoding shape: {encoding.shape}")
    
    # Test logging attendance
    print("\n3. Testing attendance logging...")
    csv_manager.log_attendance(user1_id, "John Doe", 95.5)
    csv_manager.log_attendance(user2_id, "Jane Smith", 87.2)
    csv_manager.log_attendance(user1_id, "John Doe", 92.1)
    
    print("✓ Logged 3 attendance records")
    
    # Test getting attendance logs
    print("\n4. Testing attendance retrieval...")
    logs = csv_manager.get_attendance_logs(limit=10)
    print(f"✓ Retrieved {len(logs)} attendance logs")
    
    for name, timestamp, confidence in logs:
        print(f"  - {name}: {timestamp} (Confidence: {confidence}%)")
    
    # Test date filtering
    print("\n5. Testing date filtering...")
    from datetime import datetime
    today = datetime.now().strftime('%Y-%m-%d')
    today_logs = csv_manager.get_attendance_by_date(today)
    print(f"✓ Retrieved {len(today_logs)} logs for today ({today})")
    
    # Cleanup test files
    print("\n6. Cleaning up test files...")
    test_files = ['test_users.csv', 'test_attendance.csv', 'test_encodings.pkl']
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✓ Removed {file}")
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! CSV storage system is working correctly.")
    return True

if __name__ == "__main__":
    try:
        success = test_csv_system()
        if success:
            print("\n🎉 The Face Recognition Attendance System is ready to use!")
            print("\nNext steps:")
            print("1. Install Python dependencies: pip install -r backend/requirements.txt")
            print("2. Start the backend: python backend/app.py")
            print("3. Open frontend/index.html in your web browser")
        else:
            print("\n❌ Tests failed. Please check the implementation.")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        print("Please check that all files are in place and try again.")
