/**
 * Face Recognition Attendance System - Frontend JavaScript
 * Handles webcam access, image capture, API communication, and UI interactions
 */

// Configuration
const API_BASE_URL = 'http://localhost:5000';
const RECOGNITION_INTERVAL = 2000; // 2 seconds between recognition attempts

// Global variables
let enrollStream = null;
let attendanceStream = null;
let recognitionInterval = null;
let isRecognitionActive = false;

// DOM Elements
const elements = {
    // Tab navigation
    tabBtns: document.querySelectorAll('.tab-btn'),
    tabContents: document.querySelectorAll('.tab-content'),
    
    // Enrollment elements
    userName: document.getElementById('userName'),
    enrollVideo: document.getElementById('enrollVideo'),
    enrollCanvas: document.getElementById('enrollCanvas'),
    startEnrollCamera: document.getElementById('startEnrollCamera'),
    capturePhoto: document.getElementById('capturePhoto'),
    enrollUser: document.getElementById('enrollUser'),
    capturedImage: document.getElementById('capturedImage'),
    previewImage: document.getElementById('previewImage'),
    
    // Attendance elements
    attendanceVideo: document.getElementById('attendanceVideo'),
    attendanceCanvas: document.getElementById('attendanceCanvas'),
    startAttendance: document.getElementById('startAttendance'),
    stopAttendance: document.getElementById('stopAttendance'),
    recognitionResults: document.getElementById('recognitionResults'),
    todayCount: document.getElementById('todayCount'),
    recognitionStatus: document.getElementById('recognitionStatus'),
    
    // Logs elements
    dateFilter: document.getElementById('dateFilter'),
    filterLogs: document.getElementById('filterLogs'),
    clearFilter: document.getElementById('clearFilter'),
    refreshLogs: document.getElementById('refreshLogs'),
    attendanceTableBody: document.getElementById('attendanceTableBody'),
    
    // UI elements
    connectionStatus: document.getElementById('connectionStatus'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    notifications: document.getElementById('notifications')
};

/**
 * Initialize the application
 */
function init() {
    console.log('Initializing Face Recognition Attendance System...');
    
    // Set up event listeners
    setupEventListeners();
    
    // Check backend connection
    checkBackendConnection();
    
    // Load initial data
    loadAttendanceLogs();
    updateTodayCount();
    
    console.log('Application initialized successfully');
}

/**
 * Set up all event listeners
 */
function setupEventListeners() {
    // Tab navigation
    elements.tabBtns.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
    
    // Enrollment events
    elements.startEnrollCamera.addEventListener('click', startEnrollCamera);
    elements.capturePhoto.addEventListener('click', capturePhoto);
    elements.enrollUser.addEventListener('click', enrollUser);
    
    // Attendance events
    elements.startAttendance.addEventListener('click', startAttendanceRecognition);
    elements.stopAttendance.addEventListener('click', stopAttendanceRecognition);
    
    // Logs events
    elements.filterLogs.addEventListener('click', filterLogsByDate);
    elements.clearFilter.addEventListener('click', clearDateFilter);
    elements.refreshLogs.addEventListener('click', loadAttendanceLogs);
    
    // Set today's date as default for date filter
    elements.dateFilter.value = new Date().toISOString().split('T')[0];
}

/**
 * Switch between tabs
 */
function switchTab(tabName) {
    // Update tab buttons
    elements.tabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });
    
    // Update tab content
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === tabName);
    });
    
    // Stop any active streams when switching tabs
    if (tabName !== 'enrollment' && enrollStream) {
        stopEnrollCamera();
    }
    if (tabName !== 'attendance' && attendanceStream) {
        stopAttendanceRecognition();
    }
}

/**
 * Check backend connection status
 */
async function checkBackendConnection() {
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        if (response.ok) {
            updateConnectionStatus(true);
        } else {
            updateConnectionStatus(false);
        }
    } catch (error) {
        console.error('Backend connection failed:', error);
        updateConnectionStatus(false);
    }
}

/**
 * Update connection status indicator
 */
function updateConnectionStatus(isOnline) {
    const statusElement = elements.connectionStatus;
    if (isOnline) {
        statusElement.className = 'status online';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Online';
    } else {
        statusElement.className = 'status offline';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Offline';
    }
}

/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    elements.notifications.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

/**
 * Show/hide loading overlay
 */
function showLoading(show = true) {
    elements.loadingOverlay.style.display = show ? 'flex' : 'none';
}

/**
 * Start enrollment camera
 */
async function startEnrollCamera() {
    try {
        enrollStream = await navigator.mediaDevices.getUserMedia({ 
            video: { width: 640, height: 480 } 
        });
        
        elements.enrollVideo.srcObject = enrollStream;
        elements.startEnrollCamera.disabled = true;
        elements.capturePhoto.disabled = false;
        
        showNotification('Camera started successfully', 'success');
    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Failed to access camera. Please check permissions.', 'error');
    }
}

/**
 * Stop enrollment camera
 */
function stopEnrollCamera() {
    if (enrollStream) {
        enrollStream.getTracks().forEach(track => track.stop());
        enrollStream = null;
        elements.enrollVideo.srcObject = null;
        elements.startEnrollCamera.disabled = false;
        elements.capturePhoto.disabled = true;
    }
}

/**
 * Capture photo for enrollment
 */
function capturePhoto() {
    const canvas = elements.enrollCanvas;
    const video = elements.enrollVideo;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    // Convert to base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // Show preview
    elements.previewImage.src = imageData;
    elements.capturedImage.style.display = 'block';
    elements.enrollUser.disabled = false;
    
    // Store image data for enrollment
    elements.enrollUser.dataset.imageData = imageData;
    
    showNotification('Photo captured successfully', 'success');
}

/**
 * Enroll user with captured photo
 */
async function enrollUser() {
    const name = elements.userName.value.trim();
    const imageData = elements.enrollUser.dataset.imageData;
    
    if (!name) {
        showNotification('Please enter a name', 'error');
        return;
    }
    
    if (!imageData) {
        showNotification('Please capture a photo first', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/enroll`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                image: imageData
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showNotification(`User ${name} enrolled successfully!`, 'success');
            
            // Reset form
            elements.userName.value = '';
            elements.capturedImage.style.display = 'none';
            elements.enrollUser.disabled = true;
            delete elements.enrollUser.dataset.imageData;
            
            // Stop camera
            stopEnrollCamera();
        } else {
            showNotification(result.error || 'Enrollment failed', 'error');
        }
    } catch (error) {
        console.error('Enrollment error:', error);
        showNotification('Network error during enrollment', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Start attendance recognition
 */
async function startAttendanceRecognition() {
    try {
        attendanceStream = await navigator.mediaDevices.getUserMedia({ 
            video: { width: 640, height: 480 } 
        });
        
        elements.attendanceVideo.srcObject = attendanceStream;
        elements.startAttendance.disabled = true;
        elements.stopAttendance.disabled = false;
        
        isRecognitionActive = true;
        elements.recognitionStatus.textContent = 'Active';
        
        // Start recognition loop
        recognitionInterval = setInterval(performRecognition, RECOGNITION_INTERVAL);
        
        showNotification('Attendance recognition started', 'success');
    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Failed to access camera for attendance', 'error');
    }
}

/**
 * Stop attendance recognition
 */
function stopAttendanceRecognition() {
    if (attendanceStream) {
        attendanceStream.getTracks().forEach(track => track.stop());
        attendanceStream = null;
        elements.attendanceVideo.srcObject = null;
    }
    
    if (recognitionInterval) {
        clearInterval(recognitionInterval);
        recognitionInterval = null;
    }
    
    isRecognitionActive = false;
    elements.startAttendance.disabled = false;
    elements.stopAttendance.disabled = true;
    elements.recognitionStatus.textContent = 'Stopped';
    elements.recognitionResults.innerHTML = '';
    
    // Remove visual indicators
    elements.attendanceVideo.classList.remove('face-recognized', 'face-unknown');
    
    showNotification('Attendance recognition stopped', 'info');
}

/**
 * Perform face recognition on current video frame
 */
async function performRecognition() {
    if (!isRecognitionActive || !attendanceStream) {
        return;
    }

    const canvas = elements.attendanceCanvas;
    const video = elements.attendanceVideo;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);

    // Convert to base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);

    try {
        const response = await fetch(`${API_BASE_URL}/recognize`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                image: imageData
            })
        });

        const result = await response.json();

        if (response.ok) {
            displayRecognitionResults(result);
        } else {
            console.error('Recognition error:', result.error);
        }
    } catch (error) {
        console.error('Recognition network error:', error);
    }
}

/**
 * Display recognition results on video overlay
 */
function displayRecognitionResults(result) {
    const { faces, face_count } = result;
    let resultsHTML = '';
    let hasRecognizedFace = false;
    let hasUnknownFace = false;

    if (face_count > 0) {
        resultsHTML += `<div><strong>Faces detected: ${face_count}</strong></div>`;

        faces.forEach((face, index) => {
            const { name, confidence } = face;
            const isRecognized = name !== 'Unknown' && confidence > 50;

            if (isRecognized) {
                hasRecognizedFace = true;
                resultsHTML += `<div style="color: #48bb78;">✓ ${name} (${confidence}%)</div>`;
            } else {
                hasUnknownFace = true;
                resultsHTML += `<div style="color: #f56565;">✗ Unknown Person</div>`;
            }
        });

        // Update video border based on recognition
        elements.attendanceVideo.classList.remove('face-recognized', 'face-unknown');
        if (hasRecognizedFace) {
            elements.attendanceVideo.classList.add('face-recognized');
        } else if (hasUnknownFace) {
            elements.attendanceVideo.classList.add('face-unknown');
        }

        // Update attendance logs if someone was recognized
        if (hasRecognizedFace) {
            loadAttendanceLogs();
            updateTodayCount();
        }
    } else {
        resultsHTML = '<div>No faces detected</div>';
        elements.attendanceVideo.classList.remove('face-recognized', 'face-unknown');
    }

    elements.recognitionResults.innerHTML = resultsHTML;
}

/**
 * Load attendance logs from backend
 */
async function loadAttendanceLogs() {
    try {
        const response = await fetch(`${API_BASE_URL}/attendance`);
        const result = await response.json();

        if (response.ok) {
            displayAttendanceLogs(result.attendance);
        } else {
            console.error('Failed to load attendance logs:', result.error);
        }
    } catch (error) {
        console.error('Network error loading logs:', error);
    }
}

/**
 * Display attendance logs in table
 */
function displayAttendanceLogs(logs) {
    const tbody = elements.attendanceTableBody;
    tbody.innerHTML = '';

    if (logs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">No attendance records found</td></tr>';
        return;
    }

    logs.forEach(log => {
        const row = document.createElement('tr');
        const date = new Date(log.timestamp);

        row.innerHTML = `
            <td>${log.name}</td>
            <td>${date.toLocaleDateString()}</td>
            <td>${date.toLocaleTimeString()}</td>
            <td>${log.confidence}%</td>
        `;

        tbody.appendChild(row);
    });
}

/**
 * Filter logs by selected date
 */
async function filterLogsByDate() {
    const selectedDate = elements.dateFilter.value;

    if (!selectedDate) {
        showNotification('Please select a date to filter', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/attendance?date=${selectedDate}`);
        const result = await response.json();

        if (response.ok) {
            displayAttendanceLogs(result.attendance);
            showNotification(`Showing attendance for ${selectedDate}`, 'info');
        } else {
            console.error('Failed to filter logs:', result.error);
        }
    } catch (error) {
        console.error('Network error filtering logs:', error);
    }
}

/**
 * Clear date filter and show all logs
 */
function clearDateFilter() {
    elements.dateFilter.value = '';
    loadAttendanceLogs();
    showNotification('Filter cleared, showing all records', 'info');
}

/**
 * Update today's attendance count
 */
async function updateTodayCount() {
    const today = new Date().toISOString().split('T')[0];

    try {
        const response = await fetch(`${API_BASE_URL}/attendance?date=${today}`);
        const result = await response.json();

        if (response.ok) {
            const uniqueNames = new Set(result.attendance.map(log => log.name));
            elements.todayCount.textContent = uniqueNames.size;
        }
    } catch (error) {
        console.error('Error updating today count:', error);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Check backend connection periodically
setInterval(checkBackendConnection, 30000); // Every 30 seconds
