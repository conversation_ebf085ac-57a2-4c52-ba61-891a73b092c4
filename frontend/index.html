<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Attendance System</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-user-check"></i> Face Recognition Attendance System</h1>
                <div class="status-indicator">
                    <span id="connectionStatus" class="status offline">
                        <i class="fas fa-circle"></i> Offline
                    </span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="enrollment">
                <i class="fas fa-user-plus"></i> Enrollment
            </button>
            <button class="tab-btn" data-tab="attendance">
                <i class="fas fa-camera"></i> Attendance
            </button>
            <button class="tab-btn" data-tab="logs">
                <i class="fas fa-list"></i> Attendance Logs
            </button>
        </nav>

        <!-- Enrollment Tab -->
        <div id="enrollment" class="tab-content active">
            <div class="card">
                <h2><i class="fas fa-user-plus"></i> Enroll New User</h2>
                <div class="enrollment-form">
                    <div class="form-group">
                        <label for="userName">Full Name:</label>
                        <input type="text" id="userName" placeholder="Enter full name" required>
                    </div>
                    
                    <div class="camera-section">
                        <div class="video-container">
                            <video id="enrollVideo" autoplay muted></video>
                            <canvas id="enrollCanvas" style="display: none;"></canvas>
                            <div class="camera-overlay">
                                <div class="face-guide"></div>
                            </div>
                        </div>
                        
                        <div class="camera-controls">
                            <button id="startEnrollCamera" class="btn btn-primary">
                                <i class="fas fa-camera"></i> Start Camera
                            </button>
                            <button id="capturePhoto" class="btn btn-success" disabled>
                                <i class="fas fa-camera-retro"></i> Capture Photo
                            </button>
                            <button id="enrollUser" class="btn btn-primary" disabled>
                                <i class="fas fa-user-check"></i> Enroll User
                            </button>
                        </div>
                    </div>
                    
                    <div id="capturedImage" class="captured-image" style="display: none;">
                        <h3>Captured Image:</h3>
                        <img id="previewImage" alt="Captured face">
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Tab -->
        <div id="attendance" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-camera"></i> Real-time Attendance</h2>
                <div class="attendance-section">
                    <div class="video-container">
                        <video id="attendanceVideo" autoplay muted></video>
                        <canvas id="attendanceCanvas" style="display: none;"></canvas>
                        <div class="recognition-overlay">
                            <div id="recognitionResults" class="recognition-results"></div>
                        </div>
                    </div>
                    
                    <div class="attendance-controls">
                        <button id="startAttendance" class="btn btn-success">
                            <i class="fas fa-play"></i> Start Attendance
                        </button>
                        <button id="stopAttendance" class="btn btn-danger" disabled>
                            <i class="fas fa-stop"></i> Stop Attendance
                        </button>
                    </div>
                    
                    <div class="attendance-stats">
                        <div class="stat-card">
                            <h3>Today's Attendance</h3>
                            <span id="todayCount" class="stat-number">0</span>
                        </div>
                        <div class="stat-card">
                            <h3>Recognition Status</h3>
                            <span id="recognitionStatus" class="stat-status">Stopped</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Tab -->
        <div id="logs" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-list"></i> Attendance Logs</h2>
                <div class="logs-section">
                    <div class="logs-controls">
                        <div class="filter-group">
                            <label for="dateFilter">Filter by Date:</label>
                            <input type="date" id="dateFilter">
                            <button id="filterLogs" class="btn btn-secondary">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <button id="clearFilter" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear
                            </button>
                        </div>
                        <button id="refreshLogs" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    
                    <div class="table-container">
                        <table id="attendanceTable" class="attendance-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Confidence</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceTableBody">
                                <!-- Attendance logs will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Processing...</p>
            </div>
        </div>

        <!-- Notification Container -->
        <div id="notifications" class="notifications"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
