/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 600;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.status-indicator {
    display: flex;
    align-items: center;
}

.status {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status.online {
    background: #48bb78;
    color: white;
}

.status.offline {
    background: #f56565;
    color: white;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #718096;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tab-btn i {
    margin-right: 8px;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card h2 {
    color: #4a5568;
    margin-bottom: 25px;
    font-size: 1.5rem;
    font-weight: 600;
}

.card h2 i {
    color: #667eea;
    margin-right: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #4a5568;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto 20px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.video-container video {
    width: 100%;
    height: auto;
    display: block;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.face-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 250px;
    border: 3px solid rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.recognition-overlay {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
}

.recognition-results {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 10px;
    font-size: 0.9rem;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn i {
    margin-right: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #38a169;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #e53e3e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #4a5568;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
}

/* Camera Controls */
.camera-controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

/* Captured Image */
.captured-image {
    text-align: center;
    margin-top: 20px;
}

.captured-image img {
    max-width: 300px;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Attendance Stats */
.attendance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
}

.stat-status {
    font-size: 1.2rem;
    font-weight: 500;
}

/* Logs Section */
.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-group label {
    font-weight: 500;
    color: #4a5568;
}

.filter-group input {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.attendance-table th {
    background: #667eea;
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

.attendance-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e2e8f0;
}

.attendance-table tbody tr:hover {
    background: #f7fafc;
}

.attendance-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.loading-spinner p {
    font-size: 1.2rem;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.notification {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #48bb78;
}

.notification.error {
    border-left-color: #f56565;
}

.notification.info {
    border-left-color: #667eea;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Face Recognition Indicators */
.face-recognized {
    border: 3px solid #48bb78 !important;
    box-shadow: 0 0 20px rgba(72, 187, 120, 0.5) !important;
}

.face-unknown {
    border: 3px solid #f56565 !important;
    box-shadow: 0 0 20px rgba(245, 101, 101, 0.5) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .tab-btn {
        margin-bottom: 5px;
    }

    .camera-controls {
        flex-direction: column;
        align-items: center;
    }

    .logs-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        justify-content: center;
    }

    .attendance-stats {
        grid-template-columns: 1fr;
    }
}
