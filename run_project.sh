#!/bin/bash

echo "🚀 Face Recognition Attendance System - Auto Setup & Run"
echo "========================================================="

# Install dependencies
echo "📦 Installing dependencies..."
pip install Flask flask-cors Pillow numpy

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Start the backend server
echo ""
echo "🚀 Starting backend server..."
echo "📝 Server will start in simplified mode"
echo "🌐 Frontend is already open in your browser"
echo "📝 Press Ctrl+C to stop the server"
echo ""

cd backend
python app_simple.py
