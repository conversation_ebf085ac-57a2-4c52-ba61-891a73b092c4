=======
Authors
=======

* <PERSON> <<EMAIL>>

Thanks
------

* Many, many thanks to <PERSON> (@nulhom)
  for creating dlib and for providing the trained facial feature detection and face encoding models
  used in this library.
* Thanks to everyone who works on all the awesome Python data science libraries like numpy, scipy, scikit-image,
  pillow, etc, etc that makes this kind of stuff so easy and fun in Python.
* Thanks to <PERSON><PERSON><PERSON><PERSON> and the audreyr/cookiecutter-pypackage project template
  for making Python project packaging way more tolerable.
