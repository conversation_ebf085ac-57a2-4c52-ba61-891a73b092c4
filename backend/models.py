"""
CSV-based storage models and utilities for Face Recognition Attendance System
"""
import csv
import pickle
import os
from datetime import datetime
import numpy as np

class CSVManager:
    def __init__(self, users_file='users.csv', attendance_file='attendance.csv', encodings_file='encodings.pkl'):
        """Initialize CSV file paths and create files if they don't exist"""
        self.users_file = users_file
        self.attendance_file = attendance_file
        self.encodings_file = encodings_file
        self.init_files()

    def init_files(self):
        """Create CSV files with headers if they don't exist"""
        # Create users CSV file
        if not os.path.exists(self.users_file):
            with open(self.users_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['id', 'name', 'created_at'])

        # Create attendance CSV file
        if not os.path.exists(self.attendance_file):
            with open(self.attendance_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['id', 'user_id', 'name', 'timestamp', 'confidence'])

        # Create encodings file if it doesn't exist
        if not os.path.exists(self.encodings_file):
            with open(self.encodings_file, 'wb') as file:
                pickle.dump({}, file)

    def _get_next_user_id(self):
        """Get the next available user ID"""
        try:
            with open(self.users_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                user_ids = [int(row[0]) for row in reader if row]
                return max(user_ids) + 1 if user_ids else 1
        except (FileNotFoundError, ValueError):
            return 1

    def _get_next_attendance_id(self):
        """Get the next available attendance ID"""
        try:
            with open(self.attendance_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                attendance_ids = [int(row[0]) for row in reader if row]
                return max(attendance_ids) + 1 if attendance_ids else 1
        except (FileNotFoundError, ValueError):
            return 1

    def _user_exists(self, name):
        """Check if user with given name already exists"""
        try:
            with open(self.users_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                for row in reader:
                    if row and row[1].lower() == name.lower():
                        return True
                return False
        except FileNotFoundError:
            return False

    def add_user(self, name, encoding):
        """Add a new user with their face encoding"""
        try:
            # Check if user already exists
            if self._user_exists(name):
                return None  # User already exists

            # Get next user ID
            user_id = self._get_next_user_id()

            # Add user to CSV
            with open(self.users_file, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([user_id, name, datetime.now().isoformat()])

            # Load existing encodings
            try:
                with open(self.encodings_file, 'rb') as file:
                    encodings_dict = pickle.load(file)
            except (FileNotFoundError, EOFError):
                encodings_dict = {}

            # Add new encoding
            encodings_dict[user_id] = encoding

            # Save encodings back to file
            with open(self.encodings_file, 'wb') as file:
                pickle.dump(encodings_dict, file)

            return user_id
        except Exception as e:
            print(f"Error adding user: {e}")
            return None

    def get_all_users(self):
        """Get all users and their encodings"""
        try:
            # Load users from CSV
            users_data = []
            with open(self.users_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                users_csv = list(reader)

            # Load encodings
            try:
                with open(self.encodings_file, 'rb') as file:
                    encodings_dict = pickle.load(file)
            except (FileNotFoundError, EOFError):
                encodings_dict = {}

            # Combine user data with encodings
            for row in users_csv:
                if row:  # Skip empty rows
                    user_id = int(row[0])
                    name = row[1]
                    if user_id in encodings_dict:
                        encoding = encodings_dict[user_id]
                        users_data.append((user_id, name, encoding))

            return users_data
        except FileNotFoundError:
            return []

    def log_attendance(self, user_id, name, confidence):
        """Log attendance for a user"""
        try:
            attendance_id = self._get_next_attendance_id()
            timestamp = datetime.now().isoformat()

            with open(self.attendance_file, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([attendance_id, user_id, name, timestamp, confidence])
        except Exception as e:
            print(f"Error logging attendance: {e}")

    def get_attendance_logs(self, limit=100):
        """Get recent attendance logs"""
        try:
            logs = []
            with open(self.attendance_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                all_logs = list(reader)

            # Sort by timestamp (newest first) and limit results
            all_logs = [row for row in all_logs if row]  # Remove empty rows
            all_logs.sort(key=lambda x: x[3], reverse=True)  # Sort by timestamp

            # Return in format: (name, timestamp, confidence)
            for row in all_logs[:limit]:
                if len(row) >= 5:
                    logs.append((row[2], row[3], float(row[4])))

            return logs
        except FileNotFoundError:
            return []

    def get_attendance_by_date(self, date_str):
        """Get attendance logs for a specific date"""
        try:
            logs = []
            with open(self.attendance_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                all_logs = list(reader)

            # Filter by date and sort by timestamp (newest first)
            filtered_logs = []
            for row in all_logs:
                if row and len(row) >= 5:
                    timestamp = row[3]
                    # Extract date from timestamp (YYYY-MM-DD format)
                    log_date = timestamp.split('T')[0] if 'T' in timestamp else timestamp.split(' ')[0]
                    if log_date == date_str:
                        filtered_logs.append(row)

            # Sort by timestamp (newest first)
            filtered_logs.sort(key=lambda x: x[3], reverse=True)

            # Return in format: (name, timestamp, confidence)
            for row in filtered_logs:
                logs.append((row[2], row[3], float(row[4])))

            return logs
        except FileNotFoundError:
            return []
