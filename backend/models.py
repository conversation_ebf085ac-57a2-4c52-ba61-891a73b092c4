"""
Database models and utilities for Face Recognition Attendance System
"""
import sqlite3
import pickle
import os
from datetime import datetime
import numpy as np

class DatabaseManager:
    def __init__(self, db_path='attendance.db'):
        """Initialize database connection and create tables if they don't exist"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Create database tables if they don't exist"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create users table for storing face encodings
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                encoding BLOB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create attendance table for logging attendance
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                name TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                confidence REAL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_user(self, name, encoding):
        """Add a new user with their face encoding"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Convert numpy array to bytes for storage
            encoding_bytes = pickle.dumps(encoding)
            
            cursor.execute(
                'INSERT INTO users (name, encoding) VALUES (?, ?)',
                (name, encoding_bytes)
            )
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return user_id
        except sqlite3.IntegrityError:
            return None  # User already exists
    
    def get_all_users(self):
        """Get all users and their encodings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, encoding FROM users')
        users = cursor.fetchall()
        
        # Convert encodings back to numpy arrays
        user_data = []
        for user_id, name, encoding_bytes in users:
            encoding = pickle.loads(encoding_bytes)
            user_data.append((user_id, name, encoding))
        
        conn.close()
        return user_data
    
    def log_attendance(self, user_id, name, confidence):
        """Log attendance for a user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'INSERT INTO attendance (user_id, name, confidence) VALUES (?, ?, ?)',
            (user_id, name, confidence)
        )
        
        conn.commit()
        conn.close()
    
    def get_attendance_logs(self, limit=100):
        """Get recent attendance logs"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT name, timestamp, confidence 
            FROM attendance 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
        
        logs = cursor.fetchall()
        conn.close()
        
        return logs
    
    def get_attendance_by_date(self, date_str):
        """Get attendance logs for a specific date"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT name, timestamp, confidence 
            FROM attendance 
            WHERE DATE(timestamp) = ? 
            ORDER BY timestamp DESC
        ''', (date_str,))
        
        logs = cursor.fetchall()
        conn.close()
        
        return logs
