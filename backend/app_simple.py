"""
Simplified Flask Backend for Face Recognition Attendance System
This version works without face-recognition library for testing purposes
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
import base64
from PIL import Image
import io
from datetime import datetime
import os
from models import CSVManager

app = Flask(__name__)
CORS(app)

# Initialize CSV storage
db = CSVManager()

# Global variables for face recognition simulation
known_face_encodings = []
known_face_names = []
known_face_ids = []

def load_known_faces():
    """Load all known faces from CSV storage"""
    global known_face_encodings, known_face_names, known_face_ids
    
    users = db.get_all_users()
    known_face_encodings = []
    known_face_names = []
    known_face_ids = []
    
    for user_id, name, encoding in users:
        known_face_encodings.append(encoding)
        known_face_names.append(name)
        known_face_ids.append(user_id)
    
    print(f"Loaded {len(known_face_encodings)} known faces")

def decode_base64_image(base64_string):
    """Convert base64 string to PIL Image"""
    try:
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        image_bytes = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_bytes))
        
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        image_array = np.array(image)
        return image_array
    except Exception as e:
        print(f"Error decoding image: {e}")
        return None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Face Recognition API is running (simplified mode)"})

@app.route('/enroll', methods=['POST'])
def enroll_user():
    """Enroll a new user (simplified version)"""
    try:
        data = request.get_json()
        
        if not data or 'name' not in data or 'image' not in data:
            return jsonify({"error": "Name and image are required"}), 400
        
        name = data['name'].strip()
        image_data = data['image']
        
        if not name:
            return jsonify({"error": "Name cannot be empty"}), 400
        
        # Decode the base64 image to verify it's valid
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Create dummy encoding for testing (normally this would be from face_recognition)
        dummy_encoding = np.random.rand(128)
        
        # Save to CSV storage
        user_id = db.add_user(name, dummy_encoding)
        
        if user_id is None:
            return jsonify({"error": "User with this name already exists"}), 409
        
        # Reload known faces
        load_known_faces()
        
        return jsonify({
            "message": f"User {name} enrolled successfully (simplified mode)",
            "user_id": user_id
        })
        
    except Exception as e:
        print(f"Error in enroll_user: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces (simplified version)"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({"error": "Image data is required"}), 400
        
        image_data = data['image']
        
        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Simulate face recognition
        recognized_faces = []
        
        if len(known_face_names) > 0:
            # Simulate recognizing a random enrolled user with some probability
            import random
            if random.random() > 0.3:  # 70% chance of recognition
                # Pick a random enrolled user
                random_index = random.randint(0, len(known_face_names) - 1)
                name = known_face_names[random_index]
                user_id = known_face_ids[random_index]
                confidence = random.uniform(75, 95)  # Random confidence between 75-95%
                
                # Log attendance
                db.log_attendance(user_id, name, confidence)
                
                recognized_faces.append({
                    "name": name,
                    "confidence": round(confidence, 2),
                    "user_id": user_id
                })
            else:
                # Simulate unknown face
                recognized_faces.append({
                    "name": "Unknown",
                    "confidence": 0,
                    "user_id": None
                })
        else:
            # No users enrolled yet
            recognized_faces.append({
                "name": "Unknown",
                "confidence": 0,
                "user_id": None
            })
        
        return jsonify({
            "faces": recognized_faces,
            "face_count": len(recognized_faces)
        })
        
    except Exception as e:
        print(f"Error in recognize_faces: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/attendance', methods=['GET'])
def get_attendance():
    """Get attendance logs"""
    try:
        date_filter = request.args.get('date')
        limit = int(request.args.get('limit', 100))
        
        if date_filter:
            logs = db.get_attendance_by_date(date_filter)
        else:
            logs = db.get_attendance_logs(limit)
        
        formatted_logs = []
        for name, timestamp, confidence in logs:
            formatted_logs.append({
                "name": name,
                "timestamp": timestamp,
                "confidence": round(confidence, 2) if confidence else 0
            })
        
        return jsonify({"attendance": formatted_logs})
        
    except Exception as e:
        print(f"Error in get_attendance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get list of enrolled users"""
    try:
        users = db.get_all_users()
        user_list = []
        
        for user_id, name, _ in users:
            user_list.append({
                "id": user_id,
                "name": name
            })
        
        return jsonify({"users": user_list})
        
    except Exception as e:
        print(f"Error in get_users: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Load known faces on startup
    load_known_faces()
    
    print("🚀 Starting Face Recognition Attendance System Backend (Simplified Mode)...")
    print("📝 Note: This is running in simplified mode without actual face recognition.")
    print("📝 Install face-recognition library for full functionality.")
    print("\n🌐 Available endpoints:")
    print("  GET  /health - Health check")
    print("  POST /enroll - Enroll new user")
    print("  POST /recognize - Recognize faces (simulated)")
    print("  GET  /attendance - Get attendance logs")
    print("  GET  /users - Get enrolled users")
    print("\n✅ Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
