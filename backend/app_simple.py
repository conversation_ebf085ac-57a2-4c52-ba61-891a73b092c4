"""
Simplified Flask Backend for Face Recognition Attendance System
This version works without face-recognition library for testing purposes
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
import base64
from PIL import Image
import io
from datetime import datetime
import os
from models import CSVManager

app = Flask(__name__)
CORS(app)

# Initialize CSV storage
db = CSVManager()

# Global variables for face recognition simulation
known_face_encodings = []
known_face_names = []
known_face_ids = []

def load_known_faces():
    """Load all known faces from CSV storage"""
    global known_face_encodings, known_face_names, known_face_ids
    
    users = db.get_all_users()
    known_face_encodings = []
    known_face_names = []
    known_face_ids = []
    
    for user_id, name, encoding in users:
        known_face_encodings.append(encoding)
        known_face_names.append(name)
        known_face_ids.append(user_id)
    
    print(f"Loaded {len(known_face_encodings)} known faces")

def decode_base64_image(base64_string):
    """Convert base64 string to PIL Image"""
    try:
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        image_bytes = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_bytes))
        
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        image_array = np.array(image)
        return image_array
    except Exception as e:
        print(f"Error decoding image: {e}")
        return None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Face Recognition API is running (simplified mode)"})

@app.route('/enroll', methods=['POST'])
def enroll_user():
    """Enroll a new user (improved version)"""
    try:
        data = request.get_json()

        if not data or 'name' not in data or 'image' not in data:
            return jsonify({"error": "Name and image are required"}), 400

        name = data['name'].strip()
        image_data = data['image']

        if not name:
            return jsonify({"error": "Name cannot be empty"}), 400

        # Decode the base64 image to verify it's valid
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400

        # Create multiple dummy encodings to simulate different angles
        # In real implementation, you would capture multiple angles or use data augmentation
        base_encoding = np.random.rand(128)

        # Simulate slight variations for different angles (in real app, these would be actual face encodings from different angles)
        angle_variations = []
        for i in range(3):  # Create 3 variations
            variation = base_encoding + np.random.normal(0, 0.1, 128)  # Add small noise
            angle_variations.append(variation)

        # For now, we'll use the base encoding, but this shows the concept
        user_id = db.add_user(name, base_encoding)

        if user_id is None:
            return jsonify({"error": "User with this name already exists"}), 409

        # Reload known faces
        load_known_faces()

        return jsonify({
            "message": f"User {name} enrolled successfully! (Tip: For better recognition, enroll from different angles)",
            "user_id": user_id,
            "tip": "In real implementation, capture photos from front, left, and right angles for better recognition"
        })

    except Exception as e:
        print(f"Error in enroll_user: {e}")
        return jsonify({"error": "Internal server error"}), 500

# Global variable to track recent attendance (prevent duplicates)
recent_attendance = {}

def can_log_attendance(user_id, name):
    """Check if we can log attendance (prevent duplicates within 30 seconds)"""
    current_time = datetime.now()
    key = f"{user_id}_{name}"

    if key in recent_attendance:
        time_diff = (current_time - recent_attendance[key]).total_seconds()
        if time_diff < 30:  # 30 seconds cooldown
            return False

    recent_attendance[key] = current_time
    return True

@app.route('/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces (improved version with better simulation)"""
    try:
        data = request.get_json()

        if not data or 'image' not in data:
            return jsonify({"error": "Image data is required"}), 400

        image_data = data['image']

        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400

        # Simulate face recognition with improved logic
        recognized_faces = []

        if len(known_face_names) > 0:
            import random

            # Simulate better recognition (higher chance, more realistic)
            recognition_chance = random.random()

            if recognition_chance > 0.2:  # 80% chance of recognition
                # Pick a random enrolled user (simulating recognition)
                random_index = random.randint(0, len(known_face_names) - 1)
                name = known_face_names[random_index]
                user_id = known_face_ids[random_index]

                # Simulate varying confidence based on "angle" and "lighting"
                base_confidence = random.uniform(70, 95)
                angle_factor = random.uniform(0.8, 1.0)  # Simulate angle effect
                lighting_factor = random.uniform(0.9, 1.0)  # Simulate lighting effect

                final_confidence = base_confidence * angle_factor * lighting_factor
                final_confidence = max(60, min(95, final_confidence))  # Keep between 60-95%

                # Only log attendance if enough time has passed (prevent duplicates)
                attendance_logged = False
                if final_confidence > 70 and can_log_attendance(user_id, name):
                    db.log_attendance(user_id, name, final_confidence)
                    attendance_logged = True

                recognized_faces.append({
                    "name": name,
                    "confidence": round(final_confidence, 2),
                    "user_id": user_id,
                    "attendance_logged": attendance_logged
                })
            else:
                # Simulate unknown face or poor recognition
                recognized_faces.append({
                    "name": "Unknown",
                    "confidence": 0,
                    "user_id": None,
                    "attendance_logged": False
                })
        else:
            # No users enrolled yet
            recognized_faces.append({
                "name": "Unknown",
                "confidence": 0,
                "user_id": None,
                "attendance_logged": False
            })

        return jsonify({
            "faces": recognized_faces,
            "face_count": len(recognized_faces)
        })

    except Exception as e:
        print(f"Error in recognize_faces: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/attendance', methods=['GET'])
def get_attendance():
    """Get attendance logs"""
    try:
        date_filter = request.args.get('date')
        limit = int(request.args.get('limit', 100))
        
        if date_filter:
            logs = db.get_attendance_by_date(date_filter)
        else:
            logs = db.get_attendance_logs(limit)
        
        formatted_logs = []
        for name, timestamp, confidence in logs:
            formatted_logs.append({
                "name": name,
                "timestamp": timestamp,
                "confidence": round(confidence, 2) if confidence else 0
            })
        
        return jsonify({"attendance": formatted_logs})
        
    except Exception as e:
        print(f"Error in get_attendance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get list of enrolled users"""
    try:
        users = db.get_all_users()
        user_list = []
        
        for user_id, name, _ in users:
            user_list.append({
                "id": user_id,
                "name": name
            })
        
        return jsonify({"users": user_list})
        
    except Exception as e:
        print(f"Error in get_users: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Load known faces on startup
    load_known_faces()
    
    print("🚀 Starting Face Recognition Attendance System Backend (Simplified Mode)...")
    print("📝 Note: This is running in simplified mode without actual face recognition.")
    print("📝 Install face-recognition library for full functionality.")
    print("\n🌐 Available endpoints:")
    print("  GET  /health - Health check")
    print("  POST /enroll - Enroll new user")
    print("  POST /recognize - Recognize faces (simulated)")
    print("  GET  /attendance - Get attendance logs")
    print("  GET  /users - Get enrolled users")
    print("\n✅ Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
