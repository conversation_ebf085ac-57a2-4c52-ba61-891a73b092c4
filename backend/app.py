"""
Flask Backend for Face Recognition Attendance System
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import face_recognition
import cv2
import numpy as np
import base64
from PIL import Image
import io
from datetime import datetime, timedelta
import os
from models import DatabaseManager

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# Initialize database
db = DatabaseManager()

# Global variables for face recognition
known_face_encodings = []
known_face_names = []
known_face_ids = []

def load_known_faces():
    """Load all known faces from database into memory for faster recognition"""
    global known_face_encodings, known_face_names, known_face_ids
    
    users = db.get_all_users()
    known_face_encodings = []
    known_face_names = []
    known_face_ids = []
    
    for user_id, name, encoding in users:
        known_face_encodings.append(encoding)
        known_face_names.append(name)
        known_face_ids.append(user_id)
    
    print(f"Loaded {len(known_face_encodings)} known faces")

def decode_base64_image(base64_string):
    """Convert base64 string to PIL Image"""
    try:
        # Remove data URL prefix if present
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # Decode base64 to bytes
        image_bytes = base64.b64decode(base64_string)
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(image_bytes))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Convert to numpy array
        image_array = np.array(image)
        
        return image_array
    except Exception as e:
        print(f"Error decoding image: {e}")
        return None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Face Recognition API is running"})

@app.route('/enroll', methods=['POST'])
def enroll_user():
    """Enroll a new user with their face image"""
    try:
        data = request.get_json()
        
        if not data or 'name' not in data or 'image' not in data:
            return jsonify({"error": "Name and image are required"}), 400
        
        name = data['name'].strip()
        image_data = data['image']
        
        if not name:
            return jsonify({"error": "Name cannot be empty"}), 400
        
        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Find face locations and encodings
        face_locations = face_recognition.face_locations(image_array)
        
        if len(face_locations) == 0:
            return jsonify({"error": "No face detected in the image"}), 400
        
        if len(face_locations) > 1:
            return jsonify({"error": "Multiple faces detected. Please use an image with only one face"}), 400
        
        # Get face encoding
        face_encodings = face_recognition.face_encodings(image_array, face_locations)
        face_encoding = face_encodings[0]
        
        # Save to database
        user_id = db.add_user(name, face_encoding)
        
        if user_id is None:
            return jsonify({"error": "User with this name already exists"}), 409
        
        # Reload known faces
        load_known_faces()
        
        return jsonify({
            "message": f"User {name} enrolled successfully",
            "user_id": user_id
        })
        
    except Exception as e:
        print(f"Error in enroll_user: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces in the provided image"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({"error": "Image data is required"}), 400
        
        image_data = data['image']
        
        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Find face locations and encodings
        face_locations = face_recognition.face_locations(image_array)
        face_encodings = face_recognition.face_encodings(image_array, face_locations)
        
        recognized_faces = []
        
        for face_encoding in face_encodings:
            # Compare with known faces
            matches = face_recognition.compare_faces(known_face_encodings, face_encoding, tolerance=0.6)
            face_distances = face_recognition.face_distance(known_face_encodings, face_encoding)
            
            name = "Unknown"
            confidence = 0
            user_id = None
            
            if len(face_distances) > 0:
                best_match_index = np.argmin(face_distances)
                
                if matches[best_match_index]:
                    name = known_face_names[best_match_index]
                    user_id = known_face_ids[best_match_index]
                    # Convert distance to confidence (lower distance = higher confidence)
                    confidence = max(0, (1 - face_distances[best_match_index]) * 100)
                    
                    # Log attendance if confidence is high enough
                    if confidence > 50:  # Minimum confidence threshold
                        db.log_attendance(user_id, name, confidence)
            
            recognized_faces.append({
                "name": name,
                "confidence": round(confidence, 2),
                "user_id": user_id
            })
        
        return jsonify({
            "faces": recognized_faces,
            "face_count": len(face_locations)
        })
        
    except Exception as e:
        print(f"Error in recognize_faces: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/attendance', methods=['GET'])
def get_attendance():
    """Get attendance logs"""
    try:
        # Get query parameters
        date_filter = request.args.get('date')
        limit = int(request.args.get('limit', 100))
        
        if date_filter:
            logs = db.get_attendance_by_date(date_filter)
        else:
            logs = db.get_attendance_logs(limit)
        
        # Format logs for frontend
        formatted_logs = []
        for name, timestamp, confidence in logs:
            formatted_logs.append({
                "name": name,
                "timestamp": timestamp,
                "confidence": round(confidence, 2) if confidence else 0
            })
        
        return jsonify({"attendance": formatted_logs})
        
    except Exception as e:
        print(f"Error in get_attendance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get list of enrolled users"""
    try:
        users = db.get_all_users()
        user_list = []
        
        for user_id, name, _ in users:
            user_list.append({
                "id": user_id,
                "name": name
            })
        
        return jsonify({"users": user_list})
        
    except Exception as e:
        print(f"Error in get_users: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Load known faces on startup
    load_known_faces()
    
    print("Starting Face Recognition Attendance System Backend...")
    print("Available endpoints:")
    print("  GET  /health - Health check")
    print("  POST /enroll - Enroll new user")
    print("  POST /recognize - Recognize faces")
    print("  GET  /attendance - Get attendance logs")
    print("  GET  /users - Get enrolled users")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
