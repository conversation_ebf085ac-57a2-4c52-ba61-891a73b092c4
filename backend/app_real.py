"""
Real Face Recognition Backend with Multiple Angle Support
This version uses actual face_recognition library with improvements
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import face_recognition
import cv2
import numpy as np
import base64
from PIL import Image
import io
from datetime import datetime, timedelta
import os
from models import CSVManager

app = Flask(__name__)
CORS(app)

# Initialize CSV storage
db = CSVManager()

# Global variables for face recognition
known_face_encodings = []
known_face_names = []
known_face_ids = []

# Global variable to track recent attendance (prevent duplicates)
recent_attendance = {}

def load_known_faces():
    """Load all known faces from CSV storage"""
    global known_face_encodings, known_face_names, known_face_ids
    
    users = db.get_all_users()
    known_face_encodings = []
    known_face_names = []
    known_face_ids = []
    
    for user_id, name, encoding in users:
        known_face_encodings.append(encoding)
        known_face_names.append(name)
        known_face_ids.append(user_id)
    
    print(f"Loaded {len(known_face_encodings)} known faces")

def can_log_attendance(user_id, name):
    """Check if we can log attendance (prevent duplicates within 30 seconds)"""
    current_time = datetime.now()
    key = f"{user_id}_{name}"
    
    if key in recent_attendance:
        time_diff = (current_time - recent_attendance[key]).total_seconds()
        if time_diff < 30:  # 30 seconds cooldown
            return False
    
    recent_attendance[key] = current_time
    return True

def decode_base64_image(base64_string):
    """Convert base64 string to PIL Image"""
    try:
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        image_bytes = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_bytes))
        
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        image_array = np.array(image)
        return image_array
    except Exception as e:
        print(f"Error decoding image: {e}")
        return None

def enhance_image_for_recognition(image_array):
    """Enhance image quality for better face recognition"""
    # Convert to OpenCV format
    cv_image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    
    # Apply histogram equalization to improve contrast
    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
    enhanced_gray = cv2.equalizeHist(gray)
    enhanced_image = cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2RGB)
    
    return enhanced_image

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Real Face Recognition API is running"})

@app.route('/enroll', methods=['POST'])
def enroll_user():
    """Enroll a new user with face recognition"""
    try:
        data = request.get_json()
        
        if not data or 'name' not in data or 'image' not in data:
            return jsonify({"error": "Name and image are required"}), 400
        
        name = data['name'].strip()
        image_data = data['image']
        
        if not name:
            return jsonify({"error": "Name cannot be empty"}), 400
        
        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Enhance image for better recognition
        enhanced_image = enhance_image_for_recognition(image_array)
        
        # Find face locations with multiple detection methods
        face_locations = face_recognition.face_locations(enhanced_image, model="hog")
        
        # If no faces found with HOG, try CNN model
        if len(face_locations) == 0:
            face_locations = face_recognition.face_locations(enhanced_image, model="cnn")
        
        if len(face_locations) == 0:
            return jsonify({"error": "No face detected in the image. Please ensure good lighting and face is clearly visible."}), 400
        
        if len(face_locations) > 1:
            return jsonify({"error": "Multiple faces detected. Please use an image with only one face."}), 400
        
        # Get face encoding with higher precision
        face_encodings = face_recognition.face_encodings(enhanced_image, face_locations, num_jitters=10)
        
        if len(face_encodings) == 0:
            return jsonify({"error": "Could not generate face encoding. Please try with a clearer image."}), 400
        
        face_encoding = face_encodings[0]
        
        # Save to CSV storage
        user_id = db.add_user(name, face_encoding)
        
        if user_id is None:
            return jsonify({"error": "User with this name already exists"}), 409
        
        # Reload known faces
        load_known_faces()
        
        return jsonify({
            "message": f"User {name} enrolled successfully with high-precision encoding!",
            "user_id": user_id,
            "tip": "For best results, enroll additional photos from different angles using the same name"
        })
        
    except Exception as e:
        print(f"Error in enroll_user: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces with improved accuracy and duplicate prevention"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({"error": "Image data is required"}), 400
        
        image_data = data['image']
        
        # Decode the base64 image
        image_array = decode_base64_image(image_data)
        if image_array is None:
            return jsonify({"error": "Invalid image data"}), 400
        
        # Enhance image for better recognition
        enhanced_image = enhance_image_for_recognition(image_array)
        
        # Find face locations with multiple methods
        face_locations = face_recognition.face_locations(enhanced_image, model="hog")
        
        # If no faces found with HOG, try CNN model
        if len(face_locations) == 0:
            face_locations = face_recognition.face_locations(enhanced_image, model="cnn")
        
        face_encodings = face_recognition.face_encodings(enhanced_image, face_locations)
        
        recognized_faces = []
        
        for face_encoding in face_encodings:
            # Compare with known faces using lower tolerance for better accuracy
            matches = face_recognition.compare_faces(known_face_encodings, face_encoding, tolerance=0.5)
            face_distances = face_recognition.face_distance(known_face_encodings, face_encoding)
            
            name = "Unknown"
            confidence = 0
            user_id = None
            attendance_logged = False
            
            if len(face_distances) > 0:
                best_match_index = np.argmin(face_distances)
                
                if matches[best_match_index]:
                    name = known_face_names[best_match_index]
                    user_id = known_face_ids[best_match_index]
                    # Convert distance to confidence (lower distance = higher confidence)
                    confidence = max(0, (1 - face_distances[best_match_index]) * 100)
                    
                    # Log attendance only if confidence is high enough and cooldown period has passed
                    if confidence > 60 and can_log_attendance(user_id, name):
                        db.log_attendance(user_id, name, confidence)
                        attendance_logged = True
            
            recognized_faces.append({
                "name": name,
                "confidence": round(confidence, 2),
                "user_id": user_id,
                "attendance_logged": attendance_logged
            })
        
        return jsonify({
            "faces": recognized_faces,
            "face_count": len(face_locations)
        })
        
    except Exception as e:
        print(f"Error in recognize_faces: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/attendance', methods=['GET'])
def get_attendance():
    """Get attendance logs"""
    try:
        date_filter = request.args.get('date')
        limit = int(request.args.get('limit', 100))
        
        if date_filter:
            logs = db.get_attendance_by_date(date_filter)
        else:
            logs = db.get_attendance_logs(limit)
        
        formatted_logs = []
        for name, timestamp, confidence in logs:
            formatted_logs.append({
                "name": name,
                "timestamp": timestamp,
                "confidence": round(confidence, 2) if confidence else 0
            })
        
        return jsonify({"attendance": formatted_logs})
        
    except Exception as e:
        print(f"Error in get_attendance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get list of enrolled users"""
    try:
        users = db.get_all_users()
        user_list = []
        
        for user_id, name, _ in users:
            user_list.append({
                "id": user_id,
                "name": name
            })
        
        return jsonify({"users": user_list})
        
    except Exception as e:
        print(f"Error in get_users: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Load known faces on startup
    load_known_faces()
    
    print("🚀 Starting Real Face Recognition Attendance System Backend...")
    print("✨ Features:")
    print("  - Multiple angle recognition support")
    print("  - Duplicate attendance prevention (30s cooldown)")
    print("  - Enhanced image processing")
    print("  - High-precision face encoding")
    print("\n🌐 Available endpoints:")
    print("  GET  /health - Health check")
    print("  POST /enroll - Enroll new user")
    print("  POST /recognize - Recognize faces")
    print("  GET  /attendance - Get attendance logs")
    print("  GET  /users - Get enrolled users")
    print("\n✅ Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
