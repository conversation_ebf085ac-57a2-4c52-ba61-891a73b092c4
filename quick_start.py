#!/usr/bin/env python3
"""
Quick start script for Face Recognition Attendance System
"""
import subprocess
import sys
import os
import webbrowser
import time

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def main():
    print("🚀 Face Recognition Attendance System - Quick Start")
    print("=" * 50)
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
    else:
        print("⚠️  Not in a virtual environment. Consider using one.")
    
    # Install basic dependencies
    print("\n📦 Installing basic dependencies...")
    if not run_command("pip install -r backend/requirements_simple.txt", "Installing basic packages"):
        print("❌ Failed to install dependencies. Please run manually:")
        print("   pip install Flask flask-cors Pillow numpy")
        return False
    
    print("\n🎉 Dependencies installed successfully!")
    
    # Start the backend server
    print("\n🚀 Starting the backend server...")
    print("📝 The server will start in simplified mode (no actual face recognition)")
    print("📝 You can still test the UI and CSV storage functionality")
    
    # Change to backend directory and start server
    os.chdir('backend')
    
    try:
        print("\n✅ Backend server starting...")
        print("🌐 Server will be available at: http://localhost:5000")
        print("🌐 Open frontend/index.html in your browser to use the system")
        print("\n📝 Press Ctrl+C to stop the server")
        
        # Start the server
        subprocess.run([sys.executable, 'app_simple.py'])
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
