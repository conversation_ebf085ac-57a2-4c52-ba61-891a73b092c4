#!/usr/bin/env python3
"""
Installation script for Face Recognition Attendance System
Handles Python 3.12 compatibility issues and provides alternative installation methods
"""
import subprocess
import sys
import platform
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check Python version and warn about compatibility"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required")
        return False
    
    if version.major == 3 and version.minor >= 12:
        print("⚠️  Python 3.12+ detected. Some packages may need special handling.")
    
    return True

def install_system_dependencies():
    """Install system dependencies based on the operating system"""
    system = platform.system().lower()
    
    if system == "linux":
        print("\n📦 Installing Linux system dependencies...")
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y build-essential cmake",
            "sudo apt-get install -y libopenblas-dev liblapack-dev",
            "sudo apt-get install -y libx11-dev libgtk-3-dev",
            "sudo apt-get install -y python3-dev python3-pip"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Running: {cmd}"):
                print("⚠️  Some system dependencies may not have installed correctly")
                break
    
    elif system == "darwin":  # macOS
        print("\n📦 Installing macOS dependencies...")
        if not run_command("brew --version", "Checking Homebrew"):
            print("❌ Homebrew not found. Please install Homebrew first:")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
        
        run_command("brew install cmake", "Installing cmake")
    
    elif system == "windows":
        print("\n📦 Windows detected. Please ensure you have:")
        print("   - Visual Studio Build Tools or Visual Studio Community")
        print("   - CMake (download from https://cmake.org/download/)")
    
    return True

def install_python_packages():
    """Install Python packages with compatibility handling"""
    print("\n📦 Installing Python packages...")
    
    # Install basic packages first
    basic_packages = [
        "setuptools",
        "wheel",
        "Flask==2.3.3",
        "flask-cors==4.0.0",
        "opencv-python==********",
        "Pillow==10.0.1",
        "numpy==1.26.4"
    ]
    
    for package in basic_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            print(f"⚠️  Failed to install {package}")
    
    # Try to install dlib and face-recognition
    print("\n🔄 Installing face recognition packages...")
    
    # Method 1: Try direct installation
    if run_command("pip install dlib", "Installing dlib"):
        if run_command("pip install face-recognition", "Installing face-recognition"):
            print("✅ Face recognition packages installed successfully!")
            return True
    
    # Method 2: Try with conda if available
    print("\n🔄 Trying alternative installation with conda...")
    if run_command("conda --version", "Checking conda"):
        if run_command("conda install -c conda-forge dlib", "Installing dlib via conda"):
            if run_command("pip install face-recognition", "Installing face-recognition"):
                print("✅ Face recognition packages installed via conda!")
                return True
    
    # Method 3: Provide manual instructions
    print("\n⚠️  Automatic installation failed. Manual installation required:")
    print("\nFor Ubuntu/Debian:")
    print("  sudo apt-get install build-essential cmake")
    print("  sudo apt-get install libopenblas-dev liblapack-dev")
    print("  pip install dlib")
    print("  pip install face-recognition")
    
    print("\nFor macOS:")
    print("  brew install cmake")
    print("  pip install dlib")
    print("  pip install face-recognition")
    
    print("\nFor Windows:")
    print("  Install Visual Studio Build Tools")
    print("  Install CMake")
    print("  pip install dlib")
    print("  pip install face-recognition")
    
    return False

def create_simple_backend():
    """Create a simplified backend that works without face-recognition for testing"""
    print("\n🔄 Creating simplified backend for testing...")
    
    simple_app = '''"""
Simplified Flask Backend for Face Recognition Attendance System
This version works without face-recognition library for testing purposes
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import cv2
import numpy as np
import base64
from PIL import Image
import io
from datetime import datetime
import os
from models import CSVManager

app = Flask(__name__)
CORS(app)

# Initialize CSV storage
db = CSVManager()

def decode_base64_image(base64_string):
    """Convert base64 string to PIL Image"""
    try:
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        image_bytes = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_bytes))
        
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        image_array = np.array(image)
        return image_array
    except Exception as e:
        print(f"Error decoding image: {e}")
        return None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Face Recognition API is running (simplified mode)"})

@app.route('/enroll', methods=['POST'])
def enroll_user():
    """Enroll a new user (simplified version)"""
    try:
        data = request.get_json()
        
        if not data or 'name' not in data or 'image' not in data:
            return jsonify({"error": "Name and image are required"}), 400
        
        name = data['name'].strip()
        
        if not name:
            return jsonify({"error": "Name cannot be empty"}), 400
        
        # Create dummy encoding for testing
        dummy_encoding = np.random.rand(128)
        
        user_id = db.add_user(name, dummy_encoding)
        
        if user_id is None:
            return jsonify({"error": "User with this name already exists"}), 409
        
        return jsonify({
            "message": f"User {name} enrolled successfully (simplified mode)",
            "user_id": user_id
        })
        
    except Exception as e:
        print(f"Error in enroll_user: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces (simplified version)"""
    try:
        # Return dummy recognition results for testing
        return jsonify({
            "faces": [{"name": "Test User", "confidence": 85.5, "user_id": 1}],
            "face_count": 1
        })
        
    except Exception as e:
        print(f"Error in recognize_faces: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/attendance', methods=['GET'])
def get_attendance():
    """Get attendance logs"""
    try:
        date_filter = request.args.get('date')
        limit = int(request.args.get('limit', 100))
        
        if date_filter:
            logs = db.get_attendance_by_date(date_filter)
        else:
            logs = db.get_attendance_logs(limit)
        
        formatted_logs = []
        for name, timestamp, confidence in logs:
            formatted_logs.append({
                "name": name,
                "timestamp": timestamp,
                "confidence": round(confidence, 2) if confidence else 0
            })
        
        return jsonify({"attendance": formatted_logs})
        
    except Exception as e:
        print(f"Error in get_attendance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get list of enrolled users"""
    try:
        users = db.get_all_users()
        user_list = []
        
        for user_id, name, _ in users:
            user_list.append({
                "id": user_id,
                "name": name
            })
        
        return jsonify({"users": user_list})
        
    except Exception as e:
        print(f"Error in get_users: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    print("Starting Face Recognition Attendance System Backend (Simplified Mode)...")
    print("Note: This is running in simplified mode without actual face recognition.")
    print("Install face-recognition library for full functionality.")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
    
    with open('backend/app_simple.py', 'w') as f:
        f.write(simple_app)
    
    print("✅ Created simplified backend: backend/app_simple.py")
    return True

def main():
    """Main installation function"""
    print("🚀 Face Recognition Attendance System - Installation Script")
    print("=" * 60)
    
    if not check_python_version():
        return False
    
    # Install system dependencies
    install_system_dependencies()
    
    # Try to install Python packages
    success = install_python_packages()
    
    if not success:
        print("\n⚠️  Full installation failed. Creating simplified version for testing...")
        create_simple_backend()
        print("\n📝 To test the system:")
        print("   1. Run: python backend/app_simple.py")
        print("   2. Open frontend/index.html in your browser")
        print("   3. The system will work in simplified mode")
    else:
        print("\n✅ Installation completed successfully!")
        print("\n📝 To start the system:")
        print("   1. Run: python backend/app.py")
        print("   2. Open frontend/index.html in your browser")
    
    return True

if __name__ == "__main__":
    main()
