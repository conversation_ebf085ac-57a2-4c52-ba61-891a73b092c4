#!/usr/bin/env python3
"""
Simple start script for Face Recognition Attendance System
"""
import subprocess
import sys
import webbrowser
import time
import os

def main():
    print("🚀 Face Recognition Attendance System")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('backend/app_real.py'):
        print("❌ Error: Please run this script from the project root directory")
        print("   Make sure you're in the FaceAttendence folder")
        return
    
    # Open frontend in browser
    frontend_path = os.path.abspath('frontend/index.html')
    print(f"🌐 Opening frontend: file://{frontend_path}")
    webbrowser.open(f"file://{frontend_path}")
    
    # Start backend server
    print("\n🚀 Starting backend server...")
    print("📝 Features enabled:")
    print("  ✅ Real face recognition")
    print("  ✅ One attendance per person per day")
    print("  ✅ Multiple angle support")
    print("  ✅ CSV storage")
    print("\n📝 Press Ctrl+C to stop the server")
    print("=" * 40)
    
    try:
        # Change to backend directory and start server
        os.chdir('backend')
        subprocess.run([sys.executable, 'app_real.py'])
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("👋 Thank you for using Face Recognition Attendance System!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("\n💡 Make sure you have installed all dependencies:")
        print("   pip install -r requirements.txt")

if __name__ == "__main__":
    main()
